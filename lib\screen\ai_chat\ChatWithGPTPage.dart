import 'package:flutter/material.dart';
import 'package:wiffada/services/api_service.dart';
import 'chat_screen.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/cupertino.dart';
import 'DuaDetailsPage.dart';
import '../../services/content_service.dart';
import 'package:flutter_tts/flutter_tts.dart';

class ChatWithGPTPage extends StatefulWidget {
  const ChatWithGPTPage({Key? key}) : super(key: key);

  @override
  _ChatWithGPTPageState createState() => _ChatWithGPTPageState();
}

class _ChatWithGPTPageState extends State<ChatWithGPTPage> with SingleTickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  final ApiService _apiService = ApiService();
  final ContentService _contentService = ContentService();
  bool _isTyping = false;
  late AnimationController _fabController;
  final FlutterTts flutterTts = FlutterTts();

  @override
  void initState() {
    super.initState();
    _fabController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabController.forward();
    _initTts();

    // إضافة رسالة الترحيب
    _messages.add(
      ChatMessage(
        text: """مرحباً بك في المرشد الآلي من جمعية وفادة التطوعية! 👋

أنا مساعدك الشخصي من جمعية وفادة لخدمة زوار المدينة المنورة، وسأكون سعيداً بمساعدتك في:
• معرفة المزيد عن المدينة المنورة ومعالمها
• الإجابة عن استفساراتك حول المساجد والأماكن التاريخية
• تقديم معلومات عن المطاعم والفنادق القريبة
• إرشادك إلى أفضل الأماكن السياحية
• تقديم خدمات وفادة التطوعية لزوار المدينة المنورة

كيف يمكنني خدمتك اليوم؟ 🌟""",
        isUserMessage: false,
      ),
    );
  }

  Future<void> _initTts() async {
    await flutterTts.setLanguage("ar");
    await flutterTts.setSpeechRate(0.5);
    await flutterTts.setVolume(1.0);
    await flutterTts.setPitch(1.0);
  }

  Future<void> _speak(String text) async {
    await flutterTts.stop();
    await flutterTts.speak(text);
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _fabController.dispose();
    flutterTts.stop();
    super.dispose();
  }


  void _handleSubmitted(String text) async {
    if (text.trim().isEmpty) return;

    setState(() {
      _messages.add(ChatMessage(
        text: text,
        isUserMessage: true,
      ));
      _isTyping = true;
    });

    _messageController.clear();
    _scrollController.animateTo(
      0.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );

    String? contentType = _getContentType(text);
    if (contentType != null) {
      setState(() {
        _messages.add(ChatMessage(
          text: "أبشر ✨\nسوف يتم نقلك الآن إلى صفحة تفصيلية...",
          isUserMessage: false,
        ));
        _isTyping = false;
      });

      await Future.delayed(const Duration(seconds: 1));
      _navigateToDetailsPage(contentType);
      return;
    }

    try {
      final response = await _apiService.sendMessageToChatGPT(text);
      setState(() {
        _messages.add(ChatMessage(text: response, isUserMessage: false));
        _isTyping = false;
      });

      _scrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    } catch (e) {
      setState(() {
        _messages.add(ChatMessage(
          text: 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.',
          isUserMessage: false,
        ));
        _isTyping = false;
      });
    }
  }

  String? _getContentType(String text) {
    final contentTypes = {
      'duas': ['الأدعية المأثورة', 'الأدعية', 'دعاء'],
      'adab': ['آداب زيارة المسجد النبوي', 'آداب الزيارة'],
      'rawdah': ['الروضة الشريفة', 'الروضة'],
      'fadl': ['فضل المدينة', 'فضائل المدينة'],
    };

    for (var entry in contentTypes.entries) {
      if (entry.value.any((keyword) => text.contains(keyword))) {
        return entry.key;
      }
    }
    return null;
  }

  void _navigateToDetailsPage(String contentType) async {
    final content = await _contentService.getContentByType(contentType);
    if (content != null) {
      Navigator.push(
        // ignore: use_build_context_synchronously
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => DuaDetailsPage(
            content: content,
          ),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  /// Initiates a navigation to the ChatScreen with a slide transition.
  ///
  /// This method pushes the ChatScreen onto the navigation stack using a
  /// [PageRouteBuilder]. The transition is a slide from the bottom to the top
  /// of the screen, implemented using a [SlideTransition] with a cubic ease-out
  /// curve. The transition duration is 500 milliseconds.

  void _startVoiceChat() {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const ChatScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.easeOutCubic;
          var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth < 360;

    return Scaffold(
      appBar: AppBar(
        // leading: IconButton(
        //   icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
        //   onPressed: () => Navigator.of(context).pop(),
        // ),
        actions: [
          Padding(
            padding: EdgeInsets.all(isSmallScreen ? 10.0 : 12.0),
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: ClipOval(
                child: Image.asset(
                  'assets/images/wifada_logo.png',
                  width: isSmallScreen ? 28 : 32,
                  height: isSmallScreen ? 28 : 32,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        ],
        toolbarHeight: 65,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        title: Text(
          'المرشد الآلي',
          style: GoogleFonts.ibmPlexSansArabic(
            fontSize: isSmallScreen ? 20 : 22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        backgroundColor: const Color(0xFF4F908E),
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF4F908E).withOpacity(0.15),
              Colors.white.withOpacity(0.5),
              Colors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: Column(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.7),
                ),
                child: ListView.builder(
                  controller: _scrollController,
                  reverse: true,
                  padding: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.04,
                    vertical: screenHeight * 0.02,
                  ),
                  itemCount: _messages.length + (_isTyping ? 1 : 0),
                  itemBuilder: (context, index) {
                    final messageIndex = (_messages.length - 1) - (index - (_isTyping ? 1 : 0));

                    if (_isTyping && index == 0) {
                      return Container(
                        padding: const EdgeInsets.all(16),
                        margin: EdgeInsets.only(bottom: 8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CupertinoActivityIndicator(),
                            SizedBox(width: 8),
                            Text(
                              'جاري الكتابة...',
                              style: TextStyle(
                                color: Color(0xFF4F908E),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    final message = _messages[messageIndex];
                    return Padding(
                      padding: EdgeInsets.only(
                        bottom: screenHeight * 0.015,
                        left: message.isUserMessage ? screenWidth * 0.15 : 0,
                        right: message.isUserMessage ? 0 : screenWidth * 0.15,
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: message.isUserMessage
                                ? [
                                    const Color(0xFF4F908E),
                                    const Color(0xFF3D7A79),
                                  ]
                                : [
                                    Colors.white,
                                    Colors.white.withOpacity(0.9),
                                  ],
                          ),
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.04,
                          vertical: screenHeight * 0.015,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            if (!message.isUserMessage) // فقط لرسائل AI
                              Align(
                                alignment: Alignment.centerLeft,
                                child: IconButton(
                                  icon: const Icon(
                                    Icons.volume_up_rounded,
                                    color: Color(0xFF4F908E),
                                    size: 20,
                                  ),
                                  onPressed: () => _speak(message.text),
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(
                                    minWidth: 24,
                                    minHeight: 24,
                                  ),
                                ),
                              ),
                            Text(
                              message.text,
                              style: GoogleFonts.ibmPlexSansArabic(
                                color: message.isUserMessage
                                    ? Colors.white
                                    : Colors.black87,
                                fontSize: 16,
                              ),
                              textDirection: TextDirection.rtl,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            // _buildSuggestionsStrip(),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.04,
                vertical: screenHeight * 0.015,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: const Color(0xFF4F908E).withOpacity(0.2),
                        ),
                      ),
                      child: Row(
                        children: [
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(25),
                              onTap: _startVoiceChat,
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                child: const Icon(
                                   Icons.auto_awesome,
                                  color: Color(0xFF4F908E),
                                  size: 24,
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: TextField(
                              controller: _messageController,
                              textDirection: TextDirection.rtl,
                              decoration: InputDecoration(
                                hintText: 'اكتب رسالتك هنا...',
                                hintStyle: GoogleFonts.ibmPlexSansArabic(
                                  color: Colors.grey[400],
                                ),
                                border: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 10,
                                ),
                              ),
                              onSubmitted: _handleSubmitted,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Material(
                    color: const Color(0xFF4F908E),
                    borderRadius: BorderRadius.circular(25),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(25),
                      onTap: () => _handleSubmitted(_messageController.text),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        child: const Icon(
                          Icons.send,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: null, // إزالة الزر العائم
    );
  }

  Widget _buildSuggestionChip(String label, String question) {
    double screenWidth = MediaQuery.of(context).size.width;
    double maxWidth = screenWidth < 360 ? screenWidth * 0.35 : screenWidth * 0.4;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _messageController.text = question;
            _handleSubmitted(question);
          });
        },
        child: Container(
          constraints: BoxConstraints(
            maxWidth: maxWidth,
            minWidth: 100,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.03,
            vertical: 8,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                const Color(0xFF4F908E).withOpacity(0.15),
                const Color(0xFF4F908E).withOpacity(0.25),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: const Color(0xFF4F908E).withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Text(
            label,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: screenWidth < 360 ? 11 : 13,
              color: const Color(0xFF4F908E),
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  // Widget _buildSuggestionsStrip() {
  //   double screenWidth = MediaQuery.of(context).size.width;
  //   double stripHeight = screenWidth < 360 ? 45 : 40;

  //   return Container(
  //     height: stripHeight,
  //     margin: EdgeInsets.symmetric(
  //       horizontal: screenWidth * 0.02,
  //       vertical: 4,
  //     ),
  //     child: ListView(
  //       scrollDirection: Axis.horizontal,
  //       physics: const BouncingScrollPhysics(),
  //       padding: EdgeInsets.symmetric(
  //         horizontal: screenWidth * 0.02,
  //       ),
  //       children: [
  //            _buildSuggestionChip(
  //           'ماهي خدمات الجمعية',
  //           'ماهي خدمات الجمعية؟',
  //         ),
  //         _buildSuggestionChip(
  //           'الأدعية المأثورة 📿',
  //           'ما هي الأدعية المأثورة في المسجد النبوي وأماكن استجابة الدعاء؟',
  //         ),
  //         _buildSuggestionChip(
  //           'قصص السيرة 📚',
  //           'حدثني عن قصص من السيرة النبوية حدثت في المدينة المنورة',
  //         ),
  //         _buildSuggestionChip(
  //           'مطاعم شعبية 🍖',
  //           'ما هي أشهر المطاعم الشعبية في المدينة المنورة؟',
  //         ),
  //         _buildSuggestionChip(
  //           'آداب الزيارة 🕌',
  //           'ما هي آداب زيارة المسجد النبوي الشريف؟',
  //         ),
  //         _buildSuggestionChip(
  //           'معالم إسلامية 🏛️',
  //           'ما هي أهم المعالم الإسلامية في المدينة؟',
  //         ),
  //         _buildSuggestionChip(
  //           'أماكن التسوق 🛍️',
  //           'أين أفضل أماكن التسوق والهدايا في المدينة المنورة؟',
  //         ),
  //         _buildSuggestionChip(
  //           'فضل المدينة ⭐',
  //           'حدثني عن فضل المدينة المنورة',
  //         ),
  //         _buildSuggestionChip(
  //           'الروضة الشريفة 🌸',
  //           'ما هي الروضة الشريفة وما فضلها؟',
  //         ),
  //       ],
  //     ),
  //   );
  // }
}

class ChatMessage {
  final String text;
  final bool isUserMessage;

  ChatMessage({
    required this.text,
    required this.isUserMessage,
  });
}
