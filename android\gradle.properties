# تحسين استخدام الذاكرة لـ Gradle
org.gradle.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true

# إعدادات Android
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=true
android.enableBuildCache=true

# تحسين الأداء
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.caching.enabled=true
