import 'dart:developer';

import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'realtime_chat_service.dart';

class WebRTCService {

  RTCPeerConnection? _peerConnection;

  MediaStream? _localStream;

  Function(MediaStream)? onRemoteStream;


  Future<void> initialize() async {

    final configuration = {
      'iceServers': [
        {
          'urls': [
            'stun:stun1.l.google.com:19302',
            'stun:stun2.l.google.com:19302'
          ]
        }
      ],
      'sdpSemantics': 'unified-plan',
      'enableDtlsSrtp': true, // تشفير الاتصال
      'bundlePolicy': 'max-bundle', // تجميع القنوات لتحسين الأداء
      'rtcpMuxPolicy': 'require', // دمج بروتوكولات التحكم والبيانات
    };

    // إنشاء اتصال النظير باستخدام الإعدادات المحددة
    _peerConnection = await createPeerConnection(configuration);

    // إعداد بث الصوت مع قيود محددة لتحسين جودة الصوت
    final mediaConstraints = {
      'audio': {
        'echoCancellation': true, // إلغاء الصدى
        'noiseSuppression': true, // إزالة الضوضاء
        'autoGainControl': false, // تعطيل التحكم التلقائي في مستوى الصوت
        'volume': 1.0, // أقصى مستوى للصوت
        'sampleRate': 48000, // معدل عينات أعلى لجودة صوت أفضل
        'channelCount': 2, // صوت ستيريو بدلاً من أحادي
      },
      'video': false // تعطيل الفيديو لأننا نحتاج الصوت فقط
    };

    try {
      // الوصول إلى الميكروفون وإنشاء تدفق وسائط محلي
      _localStream =
          await navigator.mediaDevices.getUserMedia(mediaConstraints);

      for (var track in _localStream!.getTracks()) {
        await _peerConnection!.addTrack(track, _localStream!);
      }

      _peerConnection!.onTrack = (RTCTrackEvent event) {
        if (event.streams.isNotEmpty && onRemoteStream != null) {
          onRemoteStream!(event.streams[0]);
        }
      };
      _peerConnection!.onIceConnectionState = (RTCIceConnectionState state) {
        print('حالة اتصال ICE: $state');
      };

      _peerConnection!.onIceGatheringState = (RTCIceGatheringState state) {
        print('حالة تجميع ICE: $state');
      };

      _peerConnection!.onIceCandidate = (RTCIceCandidate candidate) {
        print('مرشح ICE: ${candidate.candidate}');
      };

      final transceivers = await _peerConnection!.getTransceivers();
      for (var transceiver in transceivers) {
        transceiver.setDirection(TransceiverDirection.SendRecv);
      }
    } catch (e) {
      log('خطأ في تهيئة WebRTC: $e');
      rethrow;
    }
  }

  Future<void> connect() async {
    try {
      final offer = await _peerConnection!.createOffer(
          {'offerToReceiveAudio': true, 'offerToReceiveVideo': false});
        
      await _peerConnection!.setLocalDescription(offer);

      await Future.delayed(const Duration(seconds: 1));


      final answer = await RealtimeChatService.connectWebRTC(offer.sdp!);

      final remoteDesc = RTCSessionDescription(
        answer,
        'answer',
      );

      await _peerConnection!.setRemoteDescription(remoteDesc);
    } catch (e) {
      log('خطأ في الاتصال بـ OpenAI: $e');
      rethrow;
    }
  }

    // Close connections
  void dispose() {
    _peerConnection?.close();

    _localStream?.dispose();
  }
}
