import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../../models/content_model.dart';

class DuaDetailsPage extends StatefulWidget {
  final ContentModel content;

  const DuaDetailsPage({
    super.key,
    required this.content,
  });

  @override
  _DuaDetailsPageState createState() => _DuaDetailsPageState();
}

// تغيير الـ mixin من SingleTickerProviderStateMixin إلى TickerProviderStateMixin
class _DuaDetailsPageState extends State<DuaDetailsPage> with TickerProviderStateMixin {
  final FlutterTts flutterTts = FlutterTts();
  final ScrollController _scrollController = ScrollController();
  bool _isFavorite = false;
  bool _isPlaying = false;
  late AnimationController _animationController;
  late TabController _tabController;

  // إضافة روابط صور ثابتة احتياطية
  static const String _fallbackPatternUrl = 'https://www.invaluable.com/blog/wp-content/uploads/sites/77/2020/01/islamic-art-patterns-e1578504855732.jpg';
  static const String _fallbackIslamicPatternUrl = 'https://www.invaluable.com/blog/wp-content/uploads/sites/77/2020/01/islamic-art-patterns-e1578504855732.jpg';

  @override
  void initState() {
    super.initState();
    _initTts();
    _animationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    // إضافة TabController
    _tabController = TabController(
      length: widget.content.sections.length,
      vsync: this,
    );
  }

  Future<void> _initTts() async {
    await flutterTts.setLanguage("ar");
    await flutterTts.setSpeechRate(0.5);
    await flutterTts.setVolume(1.0);
    await flutterTts.setPitch(1.0);
    await flutterTts.setQueueMode(1); // أضف هذا للتحكم في قائمة انتظار الصوت
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    flutterTts.stop();
    super.dispose();
  }

  Widget _buildBackground() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.rotate(
            angle: _animationController.value * 2 * 3.14,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                image: DecorationImage(
                  image: NetworkImage(_fallbackPatternUrl),
                  opacity: 0.02,
                  repeat: ImageRepeat.repeat,
                  onError: (exception, stackTrace) {
                    print('Error loading background pattern: $exception');
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildIslamicPattern() {
    return Opacity(
      opacity: 0.05,
      child: Image.network(
        _fallbackIslamicPatternUrl,
        width: 100,
        height: 100,
        errorBuilder: (context, error, stackTrace) {
          print('Error loading Islamic pattern: $error');
          return Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: const Color(0xFF4F908E).withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: widget.content.sections.length,
      child: Scaffold(
        body: Stack(
          children: [
            _buildBackground(),
            CustomScrollView(
              physics: const BouncingScrollPhysics(),
              controller: _scrollController,
              slivers: [
                // تقليل ارتفاع Header
                SliverPersistentHeader(
                  pinned: true,
                  delegate: DetailHeaderDelegate(
                    content: widget.content,
                    minHeight: 60,
                    maxHeight: 200, // تقليل الارتفاع من 350 إلى 200
                    onBackPressed: () => Navigator.pop(context),
                  ),
                ),

                // تحسين عرض المحتوى
                SliverToBoxAdapter(
                  child: Container(
                    margin: const EdgeInsets.only(top: 10), // تقليل الهامش العلوي
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(30),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 20,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        _buildEnhancedTitleSection(),
                        _buildEnhancedTabBar(),
                        // إضافة TabBarView هنا
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.6, // ارتفاع ديناميكي
                          child: TabBarView(
                            controller: _tabController,
                            children: widget.content.sections.map((section) {
                              return _buildSectionContent(section);
                            }).toList(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        floatingActionButton: _buildEnhancedFloatingActionButton(),
      ),
    );
  }

  Widget _buildEnhancedTitleSection() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 30, 20, 20),
      child: Column(
        children: [
          // Enhanced Title
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF4F908E).withOpacity(0.1),
                  Colors.white,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: const Color(0xFF4F908E).withOpacity(0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: const BoxDecoration(
                        color: Color(0xFF4F908E),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.format_quote_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        widget.content.title,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF2D3142),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildEnhancedMetaInfo(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedMetaInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildInfoChip(
          icon: Icons.access_time,
          label: 'وقت القراءة: 5 دقائق',
        ),
        _buildInfoChip(
          icon: Icons.bookmark,
          label: 'مصدر موثوق',
        ),
      ],
    );
  }

  Widget _buildEnhancedTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController, // إضافة controller هنا
        isScrollable: true,
        indicator: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF4F908E), Color(0xFF307371)],
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF4F908E).withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        labelStyle: GoogleFonts.ibmPlexSansArabic(
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelStyle: GoogleFonts.ibmPlexSansArabic(),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[600],
        tabs: widget.content.sections
            .map((section) => Tab(text: section.title))
            .toList(),
      ),
    );
  }

  Widget _buildInfoChip({required IconData icon, required String label}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF4F908E).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4F908E).withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18, color: const Color(0xFF4F908E)),
          const SizedBox(width: 8),
          Text(
            label,
            style: GoogleFonts.ibmPlexSansArabic(
              color: const Color(0xFF4F908E),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentWithTabs() {
    return DefaultTabController(
      length: widget.content.sections.length,
      child: Column(
        children: [
          // Custom Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(25),
            ),
            child: TabBar(
              isScrollable: true,
              indicator: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF4F908E), Color(0xFF307371)],
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF4F908E).withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              labelStyle: GoogleFonts.ibmPlexSansArabic(
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: GoogleFonts.ibmPlexSansArabic(),
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey[600],
              tabs: widget.content.sections
                  .map((section) => Tab(text: section.title))
                  .toList(),
            ),
          ),
          
          // Tab Content
          SizedBox(
            height: 500, // يمكن تعديل الارتفاع حسب الحاجة
            child: TabBarView(
              children: widget.content.sections.map((section) {
                return _buildSectionContent(section);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionContent(ContentSection section) {
    return AnimationLimiter(
      child: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 375),
          childAnimationBuilder: (widget) => SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(child: widget),
          ),
          children: [
            _buildDescriptionCard(section.description),
            ...section.items.map((item) => _buildEnhancedItemCard(item)).toList(),
            const SizedBox(height: 80), // إضافة مسافة في النهاية
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionCard(String description) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF4F908E).withOpacity(0.1),
            Colors.white,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4F908E).withOpacity(0.2),
        ),
      ),
      child: Text(
        description,
        style: GoogleFonts.ibmPlexSansArabic(
          fontSize: 16,
          height: 1.8,
          color: Colors.black87,
        ),
        textAlign: TextAlign.justify,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  Widget _buildEnhancedItemCard(String item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Stack(
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: [
                  Colors.white,
                  const Color(0xFFF8FAFF),
                  const Color(0xFFF0F4FF),
                ],
              ),
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF4F908E).withOpacity(0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
              border: Border.all(
                color: const Color(0xFF4F908E).withOpacity(0.12),
                width: 1.5,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // محتوى البطاقة المحسن
                _buildEnhancedCardContent(item),
                const SizedBox(height: 20),
                // شريط الأدوات المحسن
                _buildEnhancedToolbar(item),
              ],
            ),
          ),
          // الزخرفة الإسلامية المحسنة
          Positioned(
            top: 20,
            left: 20,
            child: _buildIslamicPattern(),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedCardContent(String item) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      textDirection: TextDirection.rtl,
      children: [
        // أيقونة الدعاء المزخرفة
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF4F908E), Color(0xFF307371)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF4F908E).withOpacity(0.25),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Icon(
            Icons.volunteer_activism,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        // نص الدعاء
        Expanded(
          child: Text(
            item,
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 18,
              height: 1.8,
              color: const Color(0xFF2D3142),
              letterSpacing: 0.5,
            ),
            textDirection: TextDirection.rtl,
            textAlign: TextAlign.justify,
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedToolbar(String item) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildToolbarButton(
            icon: Icons.volume_up,
            label: 'استماع',
            onPressed: () => flutterTts.speak(item),
          ),
          _buildToolbarDivider(),
          _buildToolbarButton(
            icon: Icons.copy,
            label: 'نسخ',
            onPressed: () {},
          ),
          _buildToolbarDivider(),
          _buildToolbarButton(
            icon: Icons.share,
            label: 'مشاركة',
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(15),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: const Color(0xFF4F908E),
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 13,
                  color: const Color(0xFF4F908E),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildToolbarDivider() {
    return Container(
      height: 20,
      width: 1,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF4F908E).withOpacity(0.2),
      ),
    );
  }


  Widget _buildEnhancedFloatingActionButton() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: 'favorite',
            onPressed: () {
              setState(() => _isFavorite = !_isFavorite);
            },
            backgroundColor: _isFavorite ? const Color(0xFF4F908E) : Colors.white,
            child: Icon(
              _isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _isFavorite ? Colors.white : const Color(0xFF4F908E),
            ),
          ),
          const SizedBox(width: 16),
          FloatingActionButton(
            heroTag: 'play',
            onPressed: () {
              setState(() => _isPlaying = !_isPlaying);
              _isPlaying
                  ? flutterTts.speak(widget.content.sections.first.description)
                  : flutterTts.stop();
            },
            backgroundColor: const Color(0xFF4F908E),
            child: Icon(
              _isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}

class DetailHeaderDelegate extends SliverPersistentHeaderDelegate {
  final ContentModel content;
  final double minHeight;
  final double maxHeight;
  final VoidCallback onBackPressed;

  DetailHeaderDelegate({
    required this.content,
    required this.minHeight,
    required this.maxHeight,
    required this.onBackPressed,
  });

  Widget _buildHeaderImage() {
    return Image.network(
      content.imageUrl ?? 'https://www.invaluable.com/blog/wp-content/uploads/sites/77/2020/01/islamic-art-patterns-e1578504855732.jpg',
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        print('Error loading header image: $error');
        return Container(
          color: const Color(0xFF4F908E),
          child: const Center(
            child: Icon(
              Icons.image_not_supported,
              color: Colors.white,
              size: 48,
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final progress = shrinkOffset / maxHeight;
    
    return Stack(
      fit: StackFit.expand,
      children: [
        // تحسين عرض الصورة الخلفية
        AnimatedOpacity(
          duration: const Duration(milliseconds: 150),
          opacity: 1 - progress,
          child: Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: NetworkImage(
                  content.imageUrl ?? 'https://example.com/fallback.jpg',
                ),
                fit: BoxFit.cover,
                colorFilter: ColorFilter.mode(
                  Colors.black.withOpacity(0.3),
                  BlendMode.darken,
                ),
              ),
            ),
          ),
        ),
        
        // Gradient Overlay
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.black.withOpacity(0.7),
              ],
            ),
          ),
        ),
        
        // Title and Back Button
        Positioned(
          left: 0,
          right: 0,
          bottom: 16,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  content.title,
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 28 - (progress * 6),
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: Colors.black26,
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
        
        // Back Button
        Positioned(
          top: MediaQuery.of(context).padding.top + 10,
          left: 16,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onBackPressed,
              borderRadius: BorderRadius.circular(30),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black26,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant DetailHeaderDelegate oldDelegate) {
    return content != oldDelegate.content ||
           maxHeight != oldDelegate.maxHeight ||
           minHeight != oldDelegate.minHeight;
  }
}
